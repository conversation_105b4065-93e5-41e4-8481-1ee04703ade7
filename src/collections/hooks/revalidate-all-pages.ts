import type { Composer, Concert, Critic, Program } from "@/payload-types";
import { revalidatePath, revalidateTag } from "next/cache";
import type { CollectionAfterChangeHook } from "payload";

export const revalidateAllPages: CollectionAfterChangeHook<
	Composer | Concert | Critic | Program
> = async ({ doc, collection, req: { payload, context } }) => {
	if (!context.disableRevalidate) {
		const pages = await payload.find({
			collection: "pages",
			depth: 1,
			limit: 0,
		});

		for (const page of pages.docs) {
			const path = page.slug === "home" ? "/" : `/${page.slug}`;

			payload.logger.info(`Revalidating page at path: ${path}`);

			revalidatePath(path);
			revalidateTag("pages-sitemap");
		}
	}
	return doc;
};
