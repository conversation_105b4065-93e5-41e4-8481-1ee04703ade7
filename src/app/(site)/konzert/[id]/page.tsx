import type { NextPage } from "next";
import configPromise from "@payload-config";
import { getPayload } from "payload";
import type { Concert } from "@/payload-types";
import { ConcertDetailPageClient } from "./page.client";
import { draftMode } from "next/headers";

interface IConcertDetailPageProps {
	params: Promise<{ id: string }>;
}

const ConcertDetailPage: NextPage<IConcertDetailPageProps> = async ({
	params: paramsPromise,
}) => {
	const { id } = await paramsPromise;

	let concert: Concert | null = null;

	const payload = await getPayload({ config: configPromise });
	const { isEnabled: draft } = await draftMode();

	const isIdNumeric = /^\d+$/.test(id);

	if (!isIdNumeric) {
		const result = await payload.find({
			collection: "concerts",
			depth: 1,
			draft,
			where: {
				_status: {
					equals: "published",
				},
				"dates.date": {
					greater_than_equal: new Date().toISOString(),
				},
			},
			sort: "dates.date",
			limit: 1,
		});

		concert = result.docs[0];
	} else {
		const result = await payload.findByID({
			collection: "concerts",
			id,
			depth: 1,
			draft,
		});

		concert = result;
	}

	const header = await payload.findGlobal({
		slug: "header",
	});

	const footer = await payload.findGlobal({
		slug: "footer",
	});

	return (
		<ConcertDetailPageClient
			concert={concert}
			header={header}
			footer={footer}
		/>
	);
};

export default ConcertDetailPage;
