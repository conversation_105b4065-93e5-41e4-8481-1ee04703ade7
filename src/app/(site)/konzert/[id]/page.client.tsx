"use client";

import gsap from "gsap";
import { useCallback, useEffect, useRef, useState, type FC } from "react";
import type { Concert, Footer as TFooter, Header } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import Lenis from "lenis";
import { Media } from "@/components/render/render-media";
import { useRouter } from "next/navigation";
import { Wrapper } from "@/components/layout/wrapper";
import { useRevealer } from "@/hooks/use-revealer";
import { Navigation } from "@/components/layout/navigation";
import { Footer } from "@/components/layout/footer";
import { Split } from "@/components/motion/split";

interface IConcertDetailPageClientProps {
	concert: Concert;
	header: Header;
	footer: TFooter;
}

export const ConcertDetailPageClient: FC<IConcertDetailPageClientProps> = ({
	concert,
	header,
	footer,
}) => {
	useRevealer();

	const [copiedLink, setCopiedLink] = useState(false);

	const copyLink = () => {
		navigator.clipboard.writeText(window.location.href);
		setCopiedLink(true);
		setTimeout(() => {
			setCopiedLink(false);
		}, 2000);
	};

	return (
		<>
			<Navigation header={header} hideNextConcertButton />
			<Wrapper>
				{concert && (
					<div className="grid lg:grid-cols-2">
						<div className="relative lg:pr-[12vw]">
							<Media
								resource={concert.image}
								className="aspect-[3/4] mb-8"
								imgClassName="object-cover h-full w-full"
							/>
						</div>
						<div className="px-6 lg:pt-[12vw] lg:pb-[6vw]">
							<div className="mb-8">
								<Split delay={0.25} animationOnScroll={false}>
									<h3 className="text-balance">
										<strong>{concert.title}</strong>
									</h3>
								</Split>

								<Split delay={0.5} animationOnScroll={false}>
									<p className="h3 text-balance">{concert.subline}</p>
								</Split>
							</div>
							<div className="grid lg:[grid-template-columns:max-content_1fr] lg:gap-x-4 lg:gap-y-2">
								<p>
									<strong>WANN:</strong>
								</p>
								<p className="max-lg:mb-4">{concert.formattedDateString}</p>
								<p>
									<strong>WO:</strong>
								</p>
								<p className="max-lg:mb-4">{concert.where}</p>
								<p>
									<strong>WER:</strong>
								</p>
								<p className="max-lg:mb-4">{concert.who}</p>
								<p>
									<strong>LEITUNG:</strong>
								</p>
								<p>{concert.lead}</p>
							</div>
							<div className="mt-8">
								<button
									type="button"
									onClick={copyLink}
									className="flex items-center gap-x-2 font-semibold cursor-pointer uppercase"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="32"
										height="32"
										viewBox="0 0 24 24"
									>
										{/* Icon from Google Material Icons by Material Design Authors - https://github.com/material-icons/material-icons/blob/master/LICENSE */}
										<title>share</title>
										<path
											fill="currentColor"
											d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81c1.66 0 3-1.34 3-3s-1.34-3-3-3s-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65c0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92"
										/>
									</svg>
									{copiedLink ? "Kopiert" : "Teilen"}
								</button>
							</div>
							<div className="mt-12">
								<h3 className="mb-4">
									<strong>Programm:</strong>
								</h3>
								{concert.program && <RichText data={concert.program} />}
							</div>
						</div>
					</div>
				)}
			</Wrapper>
			<Footer footer={footer} />
		</>
	);
};
