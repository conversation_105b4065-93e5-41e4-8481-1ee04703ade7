"use client";

import gsap from "gsap";
import { useLayoutEffect, useRef, useState, type FC } from "react";
import type { Concert } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import Lenis from "lenis";
import { Media } from "@/components/render/render-media";
import { useRouter } from "next/navigation";

interface IConcertDetailModalPageClientProps {
	concert: Concert;
}

declare global {
	interface Window {
		__lenis?: { stop: () => void; start: () => void } | undefined;
	}
}

export const ConcertDetailModalPageClient: FC<
	IConcertDetailModalPageClientProps
> = ({ concert }) => {
	const router = useRouter();
	const [copiedLink, setCopiedLink] = useState(false);

	// element refs (no querySelector)
	const overlayRef = useRef<HTMLDivElement | null>(null);
	const contentRef = useRef<HTMLDivElement | null>(null);
	const closeBtnRef = useRef<HTMLButtonElement | null>(null);
	const wrapperRef = useRef<HTMLDivElement | null>(null);

	// gsap + lenis refs
	const tlRef = useRef<gsap.core.Timeline | null>(null);
	const lenisRef = useRef<Lenis | null>(null);
	const rafIdRef = useRef<number | null>(null);

	const mountedOnce = useRef(false); // prevent Strict Mode double-init in dev

	const isTouchDevice =
		typeof window !== "undefined" &&
		("ontouchstart" in window || navigator.maxTouchPoints > 0);

	// Create the opening timeline before first paint
	useLayoutEffect(() => {
		if (mountedOnce.current) return;
		mountedOnce.current = true;

		const overlay = overlayRef.current;
		const content = contentRef.current;
		const button = closeBtnRef.current;

		// Pause global Lenis if present
		try {
			window.__lenis?.stop?.();
		} catch {}

		// Lock background scroll with a class to avoid style thrash
		const html = document.documentElement;
		const body = document.body;
		const prevHtmlOverflow = html.style.overflow;
		const prevBodyOverflow = body.style.overflow;
		html.style.overflow = "hidden";
		body.style.overflow = "hidden";

		// Make sure initial state is set synchronously
		gsap.set([overlay, content, button], { clearProps: "all" });
		gsap.set(overlay, { opacity: 0 });
		gsap.set(content, {
			yPercent: 50,
			opacity: 0,
			willChange: "transform,opacity",
		});
		gsap.set(button, { yPercent: 100, opacity: 0 });

		// Build timeline once
		const tl = gsap.timeline({
			defaults: { duration: 0.5, ease: "power3.out" },
			onComplete: () => {
				// init Lenis AFTER the animation to avoid competing with paints
				const wrapper = wrapperRef.current;
				const contentEl = wrapper?.querySelector(
					"[data-lenis-content]",
				) as HTMLElement | null;
				if (!wrapper || !contentEl) return;

				const lenis = new Lenis({
					wrapper,
					content: contentEl,
					duration: isTouchDevice ? 0 : 1,
					syncTouch: true,
					touchMultiplier: 1,
				});

				lenisRef.current = lenis;
				const raf = (t: number) => {
					lenis.raf(t);
					rafIdRef.current = requestAnimationFrame(raf);
				};
				rafIdRef.current = requestAnimationFrame(raf);
			},
		});

		tl.to(overlay, { opacity: 1, duration: 0.4 })
			.to(content, { yPercent: 0, opacity: 1 }, "<")
			.to(button, { yPercent: 0, opacity: 1 }, "-=0.1");

		tlRef.current = tl;

		return () => {
			if (rafIdRef.current) cancelAnimationFrame(rafIdRef.current);
			rafIdRef.current = null;
			lenisRef.current?.destroy();
			lenisRef.current = null;

			html.style.overflow = prevHtmlOverflow;
			body.style.overflow = prevBodyOverflow;

			try {
				window.__lenis?.start?.();
			} catch {}
		};
	}, [isTouchDevice]);

	const copyLink = () => {
		if (typeof window === "undefined") return;
		navigator.clipboard.writeText(window.location.href);
		setCopiedLink(true);
		setTimeout(() => setCopiedLink(false), 2000);
	};

	const handleGoBack = () => {
		// Stop Lenis first so the closing animation has a quiet main thread
		if (rafIdRef.current) cancelAnimationFrame(rafIdRef.current);
		rafIdRef.current = null;
		lenisRef.current?.destroy();
		lenisRef.current = null;

		// Reverse the timeline and then go back
		const tl = tlRef.current;
		if (tl) {
			tl.eventCallback("onReverseComplete", () => router.back());
			tl.reverse();
		} else {
			router.back();
		}
	};

	return (
		<div className="fixed inset-0 z-[1000]">
			{/* Overlay */}
			{/* biome-ignore lint/a11y/useKeyWithClickEvents: overlay click to dismiss */}
			<div
				ref={overlayRef}
				className="DialogOverlay fixed inset-0 bg-secondary/40 z-40"
				onClick={handleGoBack}
				// Avoid pointer work until visible (GSAP will animate opacity anyway)
				style={{ pointerEvents: "auto" }}
			/>

			{/* Close button */}
			<button
				ref={closeBtnRef}
				type="button"
				onClick={handleGoBack}
				className="DialogClose absolute left-1/2 -translate-x-1/2 top-5 cursor-pointer z-[51]"
			>
				<div className="size-40">
					<div className="absolute top-4 left-1/2 -translate-x-1/2 size-32">
						<span className="h-px w-full bg-secondary rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
						<span className="h-px w-full bg-secondary -rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
					</div>
					<p className="h-full w-full bg-contrast rounded-full grid place-items-center text-sm text-center">
						De Gaudi
						<br /> wieder <br />
						zuamochn
					</p>
				</div>
			</button>

			{/* Content */}
			<div
				ref={contentRef}
				className="DialogContent layout-block fixed bottom-0 left-1/2 z-50 -translate-x-1/2 bg-primary h-[90svh] rounded-t-2xl shadow-xl overflow-hidden"
				// keep will-change only on the animating wrapper (GSAP sets it)
			>
				<div
					ref={wrapperRef}
					data-lenis-wrapper
					className="h-full w-full overflow-hidden"
					style={{
						overscrollBehavior: "contain",
						WebkitOverflowScrolling: "touch",
					}}
				>
					{/* Defer heavy subtree until after open paints */}
					<div
						data-lenis-content
						className="min-h-full"
						style={{ contentVisibility: "auto" }}
					>
						{concert && (
							<div className="grid lg:grid-cols-2">
								<div className="relative lg:pr-[12vw]">
									{/* Ensure the image doesn't block first frames */}
									<Media
										resource={concert.image}
										className="aspect-[3/4] mb-8"
										imgClassName="object-cover h-full w-full"
										// if your Media component forwards, prefer lazy decode
										// decoding="async" loading="lazy" fetchPriority="low"
									/>
								</div>
								<div className="px-6 lg:pt-[12vw] pb-[6vw]">
									<div className="mb-8">
										<h3>
											<strong>{concert.title}</strong>
										</h3>
										<p className="h3">{concert.subline}</p>
									</div>

									<div className="grid lg:[grid-template-columns:max-content_1fr] lg:gap-x-4 lg:gap-y-2">
										<p>
											<strong>WANN:</strong>
										</p>
										<p className="max-lg:mb-4">{concert.formattedDateString}</p>
										<p>
											<strong>WO:</strong>
										</p>
										<p className="max-lg:mb-4">{concert.where}</p>
										<p>
											<strong>WER:</strong>
										</p>
										<p className="max-lg:mb-4">{concert.who}</p>
										<p>
											<strong>LEITUNG:</strong>
										</p>
										<p>{concert.lead}</p>
									</div>

									<div className="mt-8">
										<button
											type="button"
											onClick={copyLink}
											className="flex items-center gap-x-2 font-semibold cursor-pointer uppercase"
										>
											{/* …icon… */}
											{copiedLink ? "Kopiert" : "Teilen"}
										</button>
									</div>

									<div className="mt-12">
										<h3 className="mb-4">
											<strong>Programm:</strong>
										</h3>
										{concert.program && <RichText data={concert.program} />}
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};
