import type { NextPage } from "next";
import configPromise from "@payload-config";
import { getPayload } from "payload";
import type { Concert } from "@/payload-types";
import { ConcertDetailModalPageClient } from "./page.client";
import { draftMode } from "next/headers";

interface IConcertDetailModalPageProps {
	params: Promise<{ id: string }>;
}

const ConcertDetailModalPage: NextPage<IConcertDetailModalPageProps> = async ({
	params: paramsPromise,
}) => {
	const { id } = await paramsPromise;

	let concert: Concert | null = null;

	const payload = await getPayload({ config: configPromise });
	const { isEnabled: draft } = await draftMode();

	if (id === "next") {
		const result = await payload.find({
			collection: "concerts",
			depth: 1,
			draft,
			where: {
				_status: {
					equals: "published",
				},
				"dates.date": {
					greater_than_equal: new Date().toISOString(),
				},
			},
			sort: "dates.date",
			limit: 1,
		});

		concert = result.docs[0];
	} else {
		const result = await payload.findByID({
			collection: "concerts",
			id,
			draft,
			depth: 1,
		});

		concert = result;
	}

	return <ConcertDetailModalPageClient concert={concert} />;
};

export default ConcertDetailModalPage;
