import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";
import { ParallaxImage } from "@/components/motion/parallax-image";
import { Magnet } from "@/components/motion/magnet";
import { cn } from "@/lib/utils";

export const ContactHero: FC<Page["hero"]> = ({ contactImage, contacts }) => {
	const firstTwoContacts = contacts?.slice(0, 2);

	const modifyName = (name: string) => {
		const [first, ...last] = name.split(" ");
		return `${first}<br /> <span style="margin-left: 50%">${last.join(" ")}</span>`;
	};

	return (
		<section className="relative h-[80svh]">
			{/* Background image */}
			<div className="absolute inset-0 overflow-hidden">
				<ParallaxImage media={contactImage} />
			</div>

			<div className="layout-block relative default-grid h-full">
				{firstTwoContacts?.map(
					(contact, i) =>
						contact.id && (
							<div
								key={contact.id}
								className={cn(
									// desktop grid columns
									"col-span-6 flex items-center",
									// mobile: stack full-width
									"col-span-12 lg:col-span-6",
									// mobile: alternate left/right alignment
									!i ? "justify-start" : "justify-end",
								)}
							>
								{/* Desktop version with Magnet */}
								<div className="hidden lg:block relative">
									<h2 className="text-center">{contact.role}</h2>
									<Magnet
										padding={400}
										className={cn(
											"-mt-8 absolute",
											i === 0 ? "left-1/3" : "left-1/4",
										)}
									>
										<div className="bg-contrast size-96 rounded-full aspect-square grid place-items-center">
											<div className="text-center">
												<h3
													dangerouslySetInnerHTML={{
														__html: modifyName(contact.name),
													}}
												/>
												<p className="my-4">{contact.address}</p>
												<a
													className="underline block"
													href={`tel:${contact.phone}`}
												>
													{contact.phone}
												</a>
												<a
													className="underline block"
													href={`mailto:${contact.email}`}
												>
													{contact.email}
												</a>
											</div>
										</div>
									</Magnet>
								</div>

								{/* Mobile version without Magnet */}
								<div className="lg:hidden">
									<div
										className={cn(
											"bg-contrast rounded-full aspect-square grid place-items-center px-6 py-8",
											"w-60 h-60", // smaller bubble on mobile
										)}
									>
										<div className="text-center">
											<h3 className="text-lg font-bold">{contact.name}</h3>
											<p className="my-2 text-sm">{contact.address}</p>
											<a
												className="underline block text-sm"
												href={`tel:${contact.phone}`}
											>
												{contact.phone}
											</a>
											<a
												className="underline block text-sm"
												href={`mailto:${contact.email}`}
											>
												{contact.email}
											</a>
										</div>
									</div>
								</div>
							</div>
						),
				)}
			</div>
		</section>
	);
};
