import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";
import RichText from "@/components/render/render-rich-text";
import { Split } from "@/components/motion/split";

export const AboutHero: FC<Page["hero"]> = ({ content, title, richText }) => {
	const sanitizedTitle =
		title?.replace(
			/Hard-Chor/g,
			'<span style="white-space: nowrap;">Hard-Chor</span>',
		) || "";

	return (
		<section className="layout-block pt-16">
			<div className="relative default-grid">
				<Split delay={0.25} animationOnScroll={false}>
					<h1 className="lg:col-span-12">{title}</h1>
				</Split>
				<div className="lg:col-span-8">
					<Split delay={0.5} animationOnScroll={false}>
						<h3 className="indent-[25%] w-full">{content}</h3>
					</Split>
				</div>
				<div className="h-[35vw] lg:h-[17.5vw] relative lg:col-span-full">
					<div className="absolute scale-[1.25] top-1/2 -translate-y-1/2 translate-x-1/9 w-screen top-">
						<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
					</div>
				</div>
				<div className="lg:col-span-7 lg:col-start-6">
					{richText && <RichText data={richText} />}
				</div>
			</div>
		</section>
	);
};
