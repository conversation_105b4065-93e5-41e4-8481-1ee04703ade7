import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";
import { Split } from "@/components/motion/split";

export const ConcertHero: FC<Page["hero"]> = ({ content, title }) => {
	return (
		<section className="layout-block">
			<div className="relative default-grid pt-24 lg:pt-12">
				<Split delay={0.25} animationOnScroll={false}>
					<h1 className="lg:col-span-full max-lg:mb-4">{title}</h1>
				</Split>
				<Split delay={0.5} animationOnScroll={false}>
					<p className="lg:col-span-4 lg:col-start-9 lg:text-right">
						{content}
					</p>
				</Split>
			</div>
		</section>
	);
};
