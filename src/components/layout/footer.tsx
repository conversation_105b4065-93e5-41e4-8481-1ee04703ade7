"use client";

import type { <PERSON> } from "react";
import type { Page, Footer as TFooter } from "@/payload-types";
import { CMSLink } from "../cms-link";

interface IFooterProps {
	footer: TFooter;
}

export const Footer: FC<IFooterProps> = ({ footer }) => {
	return (
		<>
			<section className="relative max-lg:mb-12 lg:!-mt-32 pointer-events-none">
				<div className="scale-[-1.25] translate-x-1/6">
					<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
				</div>
			</section>
			<footer className="layout-block pb-8">
				<div className="default-grid">
					<div className="lg:col-span-2 mb-16">
						<div className="col-span-4 relative pl-24 origin-top-left">
							<span className="inline-block">
								<CMSLink
									type="reference"
									reference={{
										relationTo: "pages",
										value: { slug: "home" } as <PERSON>,
									}}
									className="cursor-pointer"
								>
									<span className="font-bold text-3xl">Hard-Chor</span>
								</CMSLink>
							</span>
							<img
								src="/logo.svg"
								alt="logo"
								className="pointer-events-none absolute -top-4 -left-4  w-auto h-full origin-top-left scale-200"
							/>
						</div>
					</div>
					{footer.navItems?.map((item) => (
						<div className="lg:col-span-1" key={item.id}>
							{!item.hasChildren ? (
								<CMSLink
									{...item.link}
									className="hover:underline underline-offset-2 max-lg:mb-4"
								/>
							) : (
								<>
									<span className="lg:mb-4 mb-2 inline-block">
										{item.label}
									</span>
									<ul className="max-lg:mb-4 max-lg:pl-4">
										{item.children?.map((child) => (
											<li className="lg:mb-4 mb-2 text-sm" key={child.id}>
												<CMSLink
													{...child.link}
													className="hover:underline whitespace-nowrap"
												/>
											</li>
										))}
									</ul>
								</>
							)}
						</div>
					))}
				</div>
				<div className="default-grid mt-16 lg:mt-32">
					<div className="lg:col-span-2" />
					{footer.bottomNavItems?.map((item) => (
						<div className="col-span-1" key={item.id}>
							<CMSLink
								{...item.link}
								className="whitespace-nowrap hover:underline"
							/>
						</div>
					))}
				</div>
			</footer>
		</>
	);
};
