"use client";

import type React from "react";
import {
	useState,
	useEffect,
	useRef,
	type ReactNode,
	type HTMLAttributes,
} from "react";

interface MagnetProps extends HTMLAttributes<HTMLDivElement> {
	children: ReactNode;
	padding?: number;
	disabled?: boolean;
	magnetStrength?: number;
	activeTransition?: string;
	inactiveTransition?: string;
	wrapperClassName?: string;
	innerClassName?: string;
}

export const Magnet: React.FC<MagnetProps> = ({
	children,
	padding = 100,
	disabled = false,
	magnetStrength = 4,
	activeTransition = "transform 0.75s var(--ease-out-cubic)",
	inactiveTransition = "transform 0.5s var(--ease-in-out-cubic)",
	wrapperClassName = "",
	innerClassName = "",
	...props
}) => {
	const [isActive, setIsActive] = useState<boolean>(false);
	const [position, setPosition] = useState<{ x: number; y: number }>({
		x: 0,
		y: 0,
	});
	const magnetRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (disabled) {
			setPosition({ x: 0, y: 0 });
			return;
		}

		let mouseX = 0;
		let mouseY = 0;

		const handleMouseMove = (e: MouseEvent) => {
			mouseX = e.clientX;
			mouseY = e.clientY;
			updateMagnet(mouseX, mouseY);
		};

		const handleScroll = () => {
			updateMagnet(mouseX, mouseY);
		};

		const updateMagnet = (x: number, y: number) => {
			if (!magnetRef.current) return;

			const { left, top, width, height } =
				magnetRef.current.getBoundingClientRect();
			const centerX = left + width / 2;
			const centerY = top + height / 2;

			const distX = Math.abs(centerX - x);
			const distY = Math.abs(centerY - y);

			if (distX < width / 2 + padding && distY < height / 2 + padding) {
				setIsActive(true);
				const offsetX = (x - centerX) / magnetStrength;
				const offsetY = (y - centerY) / magnetStrength;
				setPosition({ x: offsetX, y: offsetY });
			} else {
				setIsActive(false);
				setPosition({ x: 0, y: 0 });
			}
		};

		window.addEventListener("mousemove", handleMouseMove);
		window.addEventListener("scroll", handleScroll, true); // capture phase ensures deeper scrolls are caught

		return () => {
			window.removeEventListener("mousemove", handleMouseMove);
			window.removeEventListener("scroll", handleScroll, true);
		};
	}, [padding, disabled, magnetStrength]);

	const transitionStyle = isActive ? activeTransition : inactiveTransition;

	return (
		<div
			ref={magnetRef}
			className={wrapperClassName}
			style={{ position: "relative", display: "inline-block" }}
			{...props}
		>
			<div
				className={innerClassName}
				style={{
					transform: `translate3d(${position.x}px, ${position.y}px, 0)`,
					transition: transitionStyle,
					willChange: "transform",
				}}
			>
				{children}
			</div>
		</div>
	);
};
