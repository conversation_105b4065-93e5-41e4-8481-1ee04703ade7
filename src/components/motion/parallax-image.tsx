"use client";

import { useEffect, useRef, type FC } from "react";
import cn from "clsx";
import { lerp } from "@/lib/utils";
import { useLenis } from "lenis/react";
import type { Media as MediaType } from "@/payload-types";
import { Media } from "../render/render-media";

interface IParallaxImageProps extends React.HTMLAttributes<HTMLImageElement> {
	media: MediaType | string | null | undefined;
}

export const ParallaxImage: FC<IParallaxImageProps> = ({
	className,
	media,
	...props
}) => {
	const imageRef = useRef<HTMLImageElement>(null);
	const bounds = useRef<{ top: number; bottom: number } | null>(null);
	const currentTranslateY = useRef(0);
	const targetTranslateY = useRef(0);
	const rafId = useRef<number | null>(null);

	// Update bounds using requestAnimationFrame after layout is stable
	useEffect(() => {
		const updateBounds = () => {
			if (!imageRef.current) return;
			const rect = imageRef.current.getBoundingClientRect();
			bounds.current = {
				top: rect.top + window.scrollY,
				bottom: rect.bottom + window.scrollY,
			};
		};

		const raf = requestAnimationFrame(() => {
			requestAnimationFrame(updateBounds); // double RAF to wait for layout
		});

		window.addEventListener("resize", updateBounds);

		return () => {
			cancelAnimationFrame(raf);
			window.removeEventListener("resize", updateBounds);
		};
	}, []);

	// Fallback: force bounds update on first scroll (for reloads midway down)
	useEffect(() => {
		const handleFirstScroll = () => {
			if (!bounds.current && imageRef.current) {
				const rect = imageRef.current.getBoundingClientRect();
				bounds.current = {
					top: rect.top + window.scrollY,
					bottom: rect.bottom + window.scrollY,
				};
			}
			window.removeEventListener("scroll", handleFirstScroll);
		};

		window.addEventListener("scroll", handleFirstScroll, { once: true });

		return () => {
			window.removeEventListener("scroll", handleFirstScroll);
		};
	}, []);

	// Animate translateY with lerp
	useEffect(() => {
		const animate = () => {
			if (!imageRef.current) return;

			currentTranslateY.current = lerp(
				currentTranslateY.current,
				targetTranslateY.current,
				0.1,
			);

			if (
				Math.abs(currentTranslateY.current - targetTranslateY.current) > 0.01
			) {
				imageRef.current.style.transform = `translateY(${currentTranslateY.current}px) scale(1.25)`;
			}

			rafId.current = requestAnimationFrame(animate);
		};

		animate();

		return () => {
			if (rafId.current) {
				cancelAnimationFrame(rafId.current);
			}
		};
	}, []);

	// Scroll handler with Lenis
	useLenis(({ scroll }) => {
		if (!bounds.current) return;

		const relativeScroll = scroll - bounds.current.top;
		targetTranslateY.current = relativeScroll * 0.2;
	});

	return (
		<Media
			resource={media}
			imgClassName="absolute h-full w-full object-cover"
			ref={imageRef}
			{...(props as any)} // Type assertion to bypass incompatibility
			className={cn("absolute h-full w-full", className)}
			style={{
				willChange: "transform",
				transform: "translateY(0) scale(1.25)",
			}}
		/>
	);
};
