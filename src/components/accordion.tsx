"use client";

import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState, type ReactNode } from "react";

// Accordion root
export function Accordion({
	children,
	...props
}: AccordionPrimitive.AccordionMultipleProps) {
	return (
		<AccordionPrimitive.Root className="divide-y divide-black" {...props}>
			{children}
		</AccordionPrimitive.Root>
	);
}

// Accordion item
export function AccordionItem({
	value,
	children,
}: {
	value: string;
	children: ReactNode;
}) {
	return (
		<AccordionPrimitive.Item value={value} className="py-2">
			{children}
		</AccordionPrimitive.Item>
	);
}

// Accordion trigger with your layout and icon
export function AccordionTrigger({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) {
	return (
		<AccordionPrimitive.Header className="p">
			<AccordionPrimitive.Trigger
				className={cn(
					"w-full default-grid pb-4",
					"focus:outline-none group",
					className,
				)}
			>
				<div className="lg:col-start-6 lg:col-span-7 lg:row-start-1">
					{children}
				</div>
				<div className="lg:col-span-5 flex items-center lg:row-start-1">
					<div className="flex items-center gap-x-2">
						<div className="overflow-hidden size-6 relative">
							<ChevronDown
								className={cn(
									"absolute inset-0 size-6 text-muted-foreground transition-transform ease-expo-in-out duration-300",
									"group-data-[state=open]:translate-y-full",
								)}
							/>
							<ChevronUp
								className={cn(
									"absolute inset-0 size-6 text-muted-foreground transition-transform ease-expo-in-out duration-300",
									"-translate-y-full group-data-[state=open]:translate-y-0",
								)}
							/>
						</div>
						<span>
							{/* Icon text changes */}
							<AccordionPrimitive.Trigger asChild>
								<div className="relative">
									<p className="relative pointer-events-none overflow-hidden font-semibold">
										<span className="sr-only">Toggle</span>
										<span className="w-fit whitespace-nowrap inline-block group-data-[state=open]:translate-y-0 transition-transform ease-expo-in-out duration-300 -translate-y-full">
											Wieder zu damit
										</span>
										<span className="left-0 w-fit whitespace-nowrap inline-block group-data-[state=open]:translate-y-full transition-transform ease-expo-in-out duration-300 absolute">
											Mochma auf
										</span>
									</p>
									<span className="absolute h-[2px] bg-contrast w-full top-full inset-x-0 group-data-[state=closed]:w-[76%] transition-all ease-expo-in-out duration-300" />
								</div>
							</AccordionPrimitive.Trigger>
						</span>
					</div>
				</div>
			</AccordionPrimitive.Trigger>
		</AccordionPrimitive.Header>
	);
}

// Accordion content with smooth height transition
export function AccordionContent({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) {
	return (
		<AccordionPrimitive.Content
			className={cn(
				"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down duration-500 ease-in-out-expo overflow-hidden",
				className,
			)}
		>
			{children}
		</AccordionPrimitive.Content>
	);
}
