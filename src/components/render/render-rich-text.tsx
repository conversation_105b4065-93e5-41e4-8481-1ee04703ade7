import { randId } from "@/lib/utils";
import type { Page, InlineButton as TInlineButton } from "@/payload-types";
import type {
	DefaultNodeTypes,
	SerializedLinkNode,
	DefaultTypedEditorState,
	SerializedInlineBlockNode,
} from "@payloadcms/richtext-lexical";
import {
	type JSXConvertersFunction,
	LinkJSXConverter,
	RichText as ConvertRichText,
} from "@payloadcms/richtext-lexical/react";

const TEXT_FORMAT_BOLD = 1;
const TEXT_FORMAT_ITALIC = 2;
const TEXT_FORMAT_UNDERLINE = 8;
const TEXT_FORMAT_STRIKETHROUGH = 4;

import cn from "clsx";
import { CMSLink } from "../cms-link";
import { Magnet } from "../motion/magnet";
import { InlineButton } from "../inline-blocks/inline-button";

type NodeTypes = DefaultNodeTypes | SerializedInlineBlockNode<TInlineButton>;

const internalDocToHref = ({ linkNode }: { linkNode: SerializedLinkNode }) => {
	const { value, relationTo } = linkNode.fields.doc as unknown as {
		value: Page;
		relationTo: "pages";
	};
	if (typeof value !== "object") {
		throw new Error("Expected value to be an object");
	}
	const slug = value.slug;
	return `/${slug}`;
};

// Helper function to parse inline CSS styles into a React style object
function parseInlineStyles(styleString: string): React.CSSProperties {
	const styles: Record<string, string> = {};

	if (!styleString) return styles;

	// Split by semicolon and process each style declaration
	for (const declaration of styleString.split(";")) {
		const [property, value] = declaration.split(":").map((s) => s.trim());
		if (property && value) {
			// Convert kebab-case to camelCase for React
			const camelCaseProperty = property.replace(/-([a-z])/g, (_, letter) =>
				letter.toUpperCase(),
			);
			styles[camelCaseProperty] = value;
		}
	}

	return styles as React.CSSProperties;
}

const jsxConverters: JSXConvertersFunction<NodeTypes> = ({
	defaultConverters,
}) => ({
	...defaultConverters,
	text: ({ node }) => {
		const { text, format = 0, style } = node;

		let content: React.ReactNode = text;

		if (format & TEXT_FORMAT_BOLD) content = <strong>{content}</strong>;
		if (format & TEXT_FORMAT_ITALIC) content = <em>{content}</em>;
		if (format & TEXT_FORMAT_UNDERLINE) content = <u>{content}</u>;
		if (format & TEXT_FORMAT_STRIKETHROUGH) content = <s>{content}</s>;

		if (style) {
			return <span style={parseInlineStyles(style)}>{content}</span>;
		}

		return <span>{content}</span>;
	},
	inlineBlocks: {
		"inline-button": ({ node }) => {
			return <InlineButton {...node} />;
		},
	},
	...LinkJSXConverter({ internalDocToHref }),
});

type Props = {
	data: DefaultTypedEditorState;
	enableGutter?: boolean;
	enableProse?: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export default function RichText(props: Props) {
	const { className, enableProse = true, enableGutter = true, ...rest } = props;
	const id = randId();

	return (
		<>
			<ConvertRichText
				converters={jsxConverters}
				id={`richtext-${id}`}
				className={cn(
					"payload-richtext",
					{
						container: enableGutter,
						"max-w-none": !enableGutter,
						"prose md:prose-md dark:prose-invert": enableProse,
					},
					className,
				)}
				{...rest}
			/>
		</>
	);
}
