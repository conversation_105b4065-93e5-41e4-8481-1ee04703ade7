"use client";

import { useIsMobile } from "@/hooks/use-is-mobile";
import type { InlineButton as TInlineButton } from "@/payload-types";
import type { SerializedInlineBlockNode } from "@payloadcms/richtext-lexical";
import type { FC } from "react";
import { Magnet } from "../motion/magnet";
import { CMSLink } from "../cms-link";
import { cn } from "@/lib/utils";

export const InlineButton: FC<SerializedInlineBlockNode<TInlineButton>> = ({
	fields,
}) => {
	const { type, newTab, reference, url, label } = fields;

	const isMobile = useIsMobile();

	return (
		<Magnet
			padding={5000}
			disabled={isMobile}
			className={cn(isMobile && "!translate-y-2")}
		>
			<CMSLink
				type={type}
				newTab={newTab}
				reference={reference}
				url={url}
				label={label}
				appearance="inline-button"
			/>
		</Magnet>
	);
};
