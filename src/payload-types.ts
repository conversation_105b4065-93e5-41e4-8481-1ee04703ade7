/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    composers: Composer;
    concerts: Concert;
    critics: Critic;
    media: Media;
    pages: Page;
    programs: Program;
    users: User;
    redirects: Redirect;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    composers: ComposersSelect<false> | ComposersSelect<true>;
    concerts: ConcertsSelect<false> | ConcertsSelect<true>;
    critics: CriticsSelect<false> | CriticsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    pages: PagesSelect<false> | PagesSelect<true>;
    programs: ProgramsSelect<false> | ProgramsSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {
    header: Header;
    footer: Footer;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "composers".
 */
export interface Composer {
  id: string;
  name: string;
  title?: string | null;
  letter?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "concerts".
 */
export interface Concert {
  id: string;
  title: string;
  subline?: string | null;
  image?: (string | null) | Media;
  where: string;
  who: string;
  lead: string;
  dates: {
    date: string;
    startTime: string;
    id?: string | null;
  }[];
  formattedDateString?: string | null;
  cancelled?: boolean | null;
  status?: ('upcoming' | 'past') | null;
  program?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt?: string | null;
  hasTransparency?: boolean | null;
  autoCompress?: boolean | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "critics".
 */
export interface Critic {
  id: string;
  title?: string | null;
  subline?: string | null;
  author?: string | null;
  concert?: (string | null) | Concert;
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  link: {
    type?: ('reference' | 'custom') | null;
    newTab?: boolean | null;
    reference?: {
      relationTo: 'pages';
      value: string | Page;
    } | null;
    url?: string | null;
    label: string;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: string;
  title: string;
  hero: {
    type:
      | 'none'
      | 'high-impact'
      | 'low-impact'
      | 'about-hero'
      | 'concert-hero'
      | 'choir-director-hero'
      | 'contact-hero';
    title?: string | null;
    content?: string | null;
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    image?: (string | null) | Media;
    variant?: ('standard' | 'dimmed' | 'multiplied') | null;
    directorImage?: (string | Media)[] | null;
    directorReferences?:
      | {
          reference: string;
          link: string;
          id?: string | null;
        }[]
      | null;
    contacts?:
      | {
          name: string;
          role: string;
          address: string;
          phone: string;
          email: string;
          id?: string | null;
        }[]
      | null;
    contactImage?: (string | null) | Media;
  };
  layout: (
    | ProgramArchiveBlock
    | PunkScrollerBlock
    | ChoirDirectorBiographyBlock
    | AssetArchiveBlock
    | CriticArchiveBlock
    | YoutubeEmbedBlock
    | LongListBlock
    | MapsEmbedBlock
    | ConversionBlock
    | ConcertArchiveBlock
    | ContentBlock
    | ComposerArchiveBlock
    | ChoirDirectorBlock
  )[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  /**
   * Optional: Wählen Sie eine übergeordnete Seite, unter der diese Seite erscheinen soll. Zum Beispiel könnte 'Team' eine Unterseite von 'Über uns' sein.
   */
  slug?: string | null;
  parent?: (string | null) | Page;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ProgramArchiveBlock".
 */
export interface ProgramArchiveBlock {
  populateBy?: ('all' | 'selection') | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'programs';
        value: string | Program;
      }[]
    | null;
  sort?: ('DESC' | 'ASC') | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'program-archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "programs".
 */
export interface Program {
  id: string;
  title?: string | null;
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  segments?:
    | {
        title?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "PunkScrollerBlock".
 */
export interface PunkScrollerBlock {
  image: string | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'punk-scroller';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ChoirDirectorBiographyBlock".
 */
export interface ChoirDirectorBiographyBlock {
  items?:
    | {
        title: string;
        image?: (string | null) | Media;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'choir-director-biography';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "AssetArchiveBlock".
 */
export interface AssetArchiveBlock {
  items?:
    | {
        title: string;
        type: 'images' | 'audios' | 'videos';
        assets_images?:
          | {
              image: string | Media;
              title: string;
              metadata?: string | null;
              copyright?: string | null;
              id?: string | null;
            }[]
          | null;
        assets_videos?:
          | {
              source: 'youtube' | 'upload';
              yt_url?: string | null;
              yt_id?: string | null;
              video?: (string | null) | Media;
              id?: string | null;
            }[]
          | null;
        assets_audios?:
          | {
              audio: string | Media;
              title: string;
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'asset-archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CriticArchiveBlock".
 */
export interface CriticArchiveBlock {
  id?: string | null;
  blockName?: string | null;
  blockType: 'critic-archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "YoutubeEmbedBlock".
 */
export interface YoutubeEmbedBlock {
  url: string;
  yt_id?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'youtube-embed';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "LongListBlock".
 */
export interface LongListBlock {
  title: string;
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  image: string | Media;
  columns?:
    | {
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        width?: ('1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12') | null;
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'long-list';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MapsEmbedBlock".
 */
export interface MapsEmbedBlock {
  /**
   * description: 'In Google Maps auf „Teilen“ klicken, dann „Karte einbetten“ wählen und den kompletten HTML-Code (beginnend mit <iframe>) hier einfügen.
   */
  iframeCode: string;
  embedUrl?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'maps-embed';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ConversionBlock".
 */
export interface ConversionBlock {
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  variant?: ('image' | 'punk') | null;
  subtext?: string | null;
  image?: (string | null) | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'conversion';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ConcertArchiveBlock".
 */
export interface ConcertArchiveBlock {
  populateBy?: ('next' | 'upcoming' | 'selection' | 'past' | 'all') | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'concerts';
        value: string | Concert;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'concert-archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  indent?: ('none' | 'oneSixth' | 'oneThird' | 'half') | null;
  columns?:
    | {
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        width?: ('1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12') | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: string | Page;
          } | null;
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: 'default' | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ComposerArchiveBlock".
 */
export interface ComposerArchiveBlock {
  id?: string | null;
  blockName?: string | null;
  blockType: 'composer-archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ChoirDirectorBlock".
 */
export interface ChoirDirectorBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  images?: (string | Media)[] | null;
  link: {
    type?: ('reference' | 'custom') | null;
    newTab?: boolean | null;
    reference?: {
      relationTo: 'pages';
      value: string | Page;
    } | null;
    url?: string | null;
    label: string;
  };
  /**
   * Macht nur Sinn wenn dieser Block der letzte ist.
   */
  overlapWithNext?: boolean | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'choir-director';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: string;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?: {
      relationTo: 'pages';
      value: string | Page;
    } | null;
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: string;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'composers';
        value: string | Composer;
      } | null)
    | ({
        relationTo: 'concerts';
        value: string | Concert;
      } | null)
    | ({
        relationTo: 'critics';
        value: string | Critic;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'pages';
        value: string | Page;
      } | null)
    | ({
        relationTo: 'programs';
        value: string | Program;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'redirects';
        value: string | Redirect;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "composers_select".
 */
export interface ComposersSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  letter?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "concerts_select".
 */
export interface ConcertsSelect<T extends boolean = true> {
  title?: T;
  subline?: T;
  image?: T;
  where?: T;
  who?: T;
  lead?: T;
  dates?:
    | T
    | {
        date?: T;
        startTime?: T;
        id?: T;
      };
  formattedDateString?: T;
  cancelled?: T;
  status?: T;
  program?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "critics_select".
 */
export interface CriticsSelect<T extends boolean = true> {
  title?: T;
  subline?: T;
  author?: T;
  concert?: T;
  richText?: T;
  link?:
    | T
    | {
        type?: T;
        newTab?: T;
        reference?: T;
        url?: T;
        label?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  hasTransparency?: T;
  autoCompress?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hero?:
    | T
    | {
        type?: T;
        title?: T;
        content?: T;
        richText?: T;
        image?: T;
        variant?: T;
        directorImage?: T;
        directorReferences?:
          | T
          | {
              reference?: T;
              link?: T;
              id?: T;
            };
        contacts?:
          | T
          | {
              name?: T;
              role?: T;
              address?: T;
              phone?: T;
              email?: T;
              id?: T;
            };
        contactImage?: T;
      };
  layout?:
    | T
    | {
        'program-archive'?: T | ProgramArchiveBlockSelect<T>;
        'punk-scroller'?: T | PunkScrollerBlockSelect<T>;
        'choir-director-biography'?: T | ChoirDirectorBiographyBlockSelect<T>;
        'asset-archive'?: T | AssetArchiveBlockSelect<T>;
        'critic-archive'?: T | CriticArchiveBlockSelect<T>;
        'youtube-embed'?: T | YoutubeEmbedBlockSelect<T>;
        'long-list'?: T | LongListBlockSelect<T>;
        'maps-embed'?: T | MapsEmbedBlockSelect<T>;
        conversion?: T | ConversionBlockSelect<T>;
        'concert-archive'?: T | ConcertArchiveBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        'composer-archive'?: T | ComposerArchiveBlockSelect<T>;
        'choir-director'?: T | ChoirDirectorBlockSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  parent?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ProgramArchiveBlock_select".
 */
export interface ProgramArchiveBlockSelect<T extends boolean = true> {
  populateBy?: T;
  limit?: T;
  selectedDocs?: T;
  sort?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "PunkScrollerBlock_select".
 */
export interface PunkScrollerBlockSelect<T extends boolean = true> {
  image?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ChoirDirectorBiographyBlock_select".
 */
export interface ChoirDirectorBiographyBlockSelect<T extends boolean = true> {
  items?:
    | T
    | {
        title?: T;
        image?: T;
        richText?: T;
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "AssetArchiveBlock_select".
 */
export interface AssetArchiveBlockSelect<T extends boolean = true> {
  items?:
    | T
    | {
        title?: T;
        type?: T;
        assets_images?:
          | T
          | {
              image?: T;
              title?: T;
              metadata?: T;
              copyright?: T;
              id?: T;
            };
        assets_videos?:
          | T
          | {
              source?: T;
              yt_url?: T;
              yt_id?: T;
              video?: T;
              id?: T;
            };
        assets_audios?:
          | T
          | {
              audio?: T;
              title?: T;
              id?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CriticArchiveBlock_select".
 */
export interface CriticArchiveBlockSelect<T extends boolean = true> {
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "YoutubeEmbedBlock_select".
 */
export interface YoutubeEmbedBlockSelect<T extends boolean = true> {
  url?: T;
  yt_id?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "LongListBlock_select".
 */
export interface LongListBlockSelect<T extends boolean = true> {
  title?: T;
  richText?: T;
  image?: T;
  columns?:
    | T
    | {
        richText?: T;
        width?: T;
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MapsEmbedBlock_select".
 */
export interface MapsEmbedBlockSelect<T extends boolean = true> {
  iframeCode?: T;
  embedUrl?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ConversionBlock_select".
 */
export interface ConversionBlockSelect<T extends boolean = true> {
  content?: T;
  variant?: T;
  subtext?: T;
  image?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ConcertArchiveBlock_select".
 */
export interface ConcertArchiveBlockSelect<T extends boolean = true> {
  populateBy?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  indent?: T;
  columns?:
    | T
    | {
        richText?: T;
        width?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ComposerArchiveBlock_select".
 */
export interface ComposerArchiveBlockSelect<T extends boolean = true> {
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ChoirDirectorBlock_select".
 */
export interface ChoirDirectorBlockSelect<T extends boolean = true> {
  richText?: T;
  images?: T;
  link?:
    | T
    | {
        type?: T;
        newTab?: T;
        reference?: T;
        url?: T;
        label?: T;
      };
  overlapWithNext?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "programs_select".
 */
export interface ProgramsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  segments?:
    | T
    | {
        title?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: string;
  navItems?:
    | {
        isMegaNav?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: string | Page;
          } | null;
          url?: string | null;
          label: string;
        };
        label?: string | null;
        children?:
          | {
              link: {
                type?: ('reference' | 'custom') | null;
                newTab?: boolean | null;
                reference?: {
                  relationTo: 'pages';
                  value: string | Page;
                } | null;
                url?: string | null;
                label: string;
              };
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: string;
  navItems?:
    | {
        hasChildren?: boolean | null;
        label?: string | null;
        children?:
          | {
              link: {
                type?: ('reference' | 'custom') | null;
                newTab?: boolean | null;
                reference?: {
                  relationTo: 'pages';
                  value: string | Page;
                } | null;
                url?: string | null;
                label: string;
              };
              id?: string | null;
            }[]
          | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: string | Page;
          } | null;
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  bottomNavItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: string | Page;
          } | null;
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        isMegaNav?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        label?: T;
        children?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                  };
              id?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        hasChildren?: T;
        label?: T;
        children?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                  };
              id?: T;
            };
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  bottomNavItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'concerts';
          value: string | Concert;
        } | null)
      | ({
          relationTo: 'pages';
          value: string | Page;
        } | null);
    global?: string | null;
    user?: (string | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "InlineButton".
 */
export interface InlineButton {
  type?: ('reference' | 'custom') | null;
  newTab?: boolean | null;
  label: string;
  reference?: {
    relationTo: 'pages';
    value: string | Page;
  } | null;
  url?: string | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'inline-button';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}