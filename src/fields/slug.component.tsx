"use client";

import "./slug.style.scss";

import { useCallback, useEffect } from "react";
import type { TextFieldClientProps } from "payload";

import {
	useField,
	Button,
	TextInput,
	FieldLabel,
	useFormFields,
	useForm,
} from "@payloadcms/ui";

import { formatSlug } from "@/utils/format-slug";
import type { Page } from "@/payload-types";

type SlugComponentProps = {
	fieldToUse: string;
	checkboxFieldPath: string;
} & TextFieldClientProps;

export const SlugComponent: React.FC<SlugComponentProps> = ({
	field,
	fieldToUse,
	checkboxFieldPath: checkboxFieldPathFromProps,
	path,
	readOnly: readOnlyFromProps,
}) => {
	const { label } = field;

	const checkboxFieldPath = path?.includes(".")
		? `${path}.${checkboxFieldPathFromProps}`
		: checkboxFieldPathFromProps;

	const { value, setValue } = useField<string>({ path: path || field.name });

	const { dispatchFields } = useForm();

	// The value of the checkbox
	// We're using separate useFormFields to minimise re-renders
	const checkboxValue = useFormFields(([fields]) => {
		return fields[checkboxFieldPath]?.value as string;
	});

	// The value of the field we're listening to for the slug
	const targetFieldValue = useFormFields(([fields]) => {
		return fields[fieldToUse]?.value as string;
	});

	const parentSlug = useFormFields(([fields]) => {
		const parent = fields.parent?.value as Page;
		return parent?.slug || ""; // If populated
	});

	useEffect(() => {
		if (checkboxValue) {
			if (targetFieldValue) {
				const formattedSlug = formatSlug(targetFieldValue);
				const newSlug = parentSlug
					? `${parentSlug}/${formattedSlug}`
					: formattedSlug;

				if (value !== newSlug) setValue(newSlug);
			} else {
				if (value !== "") setValue("");
			}
		}
	}, [targetFieldValue, checkboxValue, parentSlug, setValue, value]);

	const handleLock = useCallback(
		(e: React.MouseEvent<Element, MouseEvent>) => {
			e.preventDefault();

			dispatchFields({
				type: "UPDATE",
				path: checkboxFieldPath,
				value: !checkboxValue,
			});
		},
		[checkboxValue, checkboxFieldPath, dispatchFields],
	);

	const readOnly = readOnlyFromProps || checkboxValue;

	return (
		<div className="field-type slug-field-component">
			<div className="label-wrapper">
				<FieldLabel htmlFor={`field-${path}`} label="Slug (URL-Pfad)" />

				<Button
					className="lock-button"
					buttonStyle="none"
					onClick={handleLock}
					aria-label={
						checkboxValue
							? "Slug bearbeiten erlauben"
							: "Automatische Slug-Generierung aktivieren"
					}
				>
					{checkboxValue ? "Entsperren" : "Sperren"}
				</Button>
			</div>

			<TextInput
				value={value}
				onChange={setValue}
				path={path || field.name}
				readOnly={Boolean(readOnly)}
			/>

			{parentSlug && (
				<p className="text-xs text-gray-500 mt-1">
					Die vollständige URL lautet: <strong>/{value}</strong>
				</p>
			)}
		</div>
	);
};
