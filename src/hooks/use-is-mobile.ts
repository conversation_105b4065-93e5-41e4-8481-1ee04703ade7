import { useEffect, useState } from "react";

export const useIsMobile = () => {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		const isTouchDevice =
			"ontouchstart" in window || navigator.maxTouchPoints > 0;

		if (isTouchDevice) {
			setIsMobile(true);
			return;
		}

		const mediaQuery = window.matchMedia("(max-width: 799.98px)");
		setIsMobile(mediaQuery.matches);

		const handleResize = () => {
			setIsMobile(mediaQuery.matches);
		};

		mediaQuery.addEventListener("change", handleResize);

		return () => {
			mediaQuery.removeEventListener("change", handleResize);
		};
	}, []);

	return isMobile;
};
