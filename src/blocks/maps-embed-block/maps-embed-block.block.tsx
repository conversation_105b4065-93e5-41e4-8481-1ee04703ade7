"use client";

import type { FC } from "react";
import type { MapsEmbedBlock as IMapsEmbedBlockProps } from "@/payload-types";

export const MapsEmbedBlock: FC<IMapsEmbedBlockProps> = ({ embedUrl }) => {
	return (
		<section className="layout-block">
			<div className="relative w-full aspect-video">
				<iframe
					src={embedUrl as string}
					width="100%"
					height="100%"
					style={{ border: 0 }}
					allowFullScreen
					loading="lazy"
					referrerPolicy="no-referrer-when-downgrade"
					className="w-full h-full"
					title="Map"
				/>
			</div>
		</section>
	);
};
