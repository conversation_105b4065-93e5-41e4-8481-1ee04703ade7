import type { Block } from "payload";

export const MapsEmbedBlockConfig: Block = {
	slug: "maps-embed",
	imageURL: "/blocks/maps-embed-block.png",
	labels: {
		singular: "Maps Embed Sektion",
		plural: "Maps Embed Sektionen",
	},
	fields: [
		{
			name: "iframeCode",
			type: "textarea",
			required: true,
			label: "Google Maps Embed Code",
			admin: {
				placeholder:
					'<iframe src="https://www.google.com/maps/embed?..."></iframe>',
				description:
					"description: 'In Google Maps auf „Teilen“ klicken, dann „Karte einbetten“ wählen und den kompletten HTML-Code (beginnend mit <iframe>) hier einfügen.",
			},
			hooks: {
				beforeValidate: [
					({ value, siblingData }) => {
						if (typeof value === "string") {
							const match = value.match(/src="([^"]+)"/);
							if (match?.[1]) {
								siblingData.embedUrl = match[1];
							}
						}
						return value;
					},
				],
			},
		},
		{
			name: "embedUrl",
			type: "text",
			required: false,
			admin: {
				readOnly: true,
				hidden: true,
			},
		},
	],
	interfaceName: "MapsEmbedBlock",
};
