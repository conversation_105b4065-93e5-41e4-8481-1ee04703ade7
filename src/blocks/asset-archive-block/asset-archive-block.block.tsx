import {
	Accordion,
	Accordion<PERSON>ontent,
	Accordion<PERSON><PERSON>,
	AccordionTrigger,
} from "@/components/accordion";
import { CMSLink } from "@/components/cms-link";
import { Media } from "@/components/render/render-media";
import { YTPlayer } from "@/components/yt-player";
import { cn } from "@/lib/utils";
import type { AssetArchiveBlock as IAssetArchiveBlockProps } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import type { FC } from "react";

export const AssetArchiveBlock: FC<IAssetArchiveBlockProps> = async ({
	items,
}) => {
	if (!items?.length) return null;

	return (
		<section className="layout-block">
			<Accordion type="multiple">
				{items.map(
					(item) =>
						item.id && (
							<AccordionItem key={item.id} value={item.id}>
								<AccordionTrigger>
									<h2>{item.title}</h2>
								</AccordionTrigger>
								<AccordionContent>
									<div className="default-grid">
										{item.type === "images" ? (
											<div className="lg:col-start-3 lg:col-span-9 lg:grid lg:grid-cols-3 lg:gap-x-[var(--gap)] max-lg:pt-4">
												<div />
												{item.assets_images?.map((i) => (
													<div key={i.id} className="max-lg:mb-4">
														<div className="w-full aspect-[16/9]">
															<Media
																resource={i.image}
																className="h-full w-auto"
																imgClassName="object-contain h-full w-full object-left"
															/>
														</div>
														<p className="mt-2">
															<strong>{i.title}</strong>
														</p>
														<p>{i.metadata}</p>
														<p>{i.copyright}</p>
														<div className="pt-2 pb-4 lg:pt-8 lg:pb-16">
															{typeof i.image === "object" ? (
																<CMSLink
																	type="custom"
																	url={i.image.url}
																	label="Download"
																	appearance="default"
																	download
																/>
															) : null}
														</div>
													</div>
												))}
											</div>
										) : null}
										{item.type === "videos" ? (
											<div className="lg:col-start-3 lg:col-span-9 lg:grid lg:grid-cols-3 lg:gap-[var(--gap)] max-lg:pt-4">
												{item.assets_videos?.map((i, index) => (
													<div
														key={i.id}
														className={cn(
															"max-lg:mb-4",
															index === 0 && "lg:col-span-full",
														)}
													>
														{i.source === "youtube" ? (
															<YTPlayer
																id={i.yt_id as string}
																title={i.yt_url as string}
															/>
														) : (
															<Media
																resource={i.video}
																className="h-full w-auto"
																imgClassName="object-contain h-full w-full object-left"
															/>
														)}
													</div>
												))}
											</div>
										) : null}
										{item.type === "audios" ? (
											<div className="lg:col-start-3 lg:col-span-9 lg:grid lg:grid-cols-3 lg:gap-[var(--gap)]">
												{item.assets_audios?.map((i) => (
													<div key={i.id}>
														<Media resource={i.audio} />
														<p className="mt-2">
															<strong>{i.title}</strong>
														</p>
													</div>
												))}
											</div>
										) : null}
									</div>
								</AccordionContent>
								<div className="border-b border-secondary" />
							</AccordionItem>
						),
				)}
			</Accordion>
		</section>
	);
};
