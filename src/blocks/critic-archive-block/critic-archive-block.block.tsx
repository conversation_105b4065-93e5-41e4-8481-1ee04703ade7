import {
	Accordion,
	Accordion<PERSON>ontent,
	Accordion<PERSON><PERSON>,
	AccordionTrigger,
} from "@/components/accordion";
import { CMSLink } from "@/components/cms-link";
import { Media } from "@/components/render/render-media";
import type { CriticArchiveBlock as ICriticArchiveBlockProps } from "@/payload-types";
import configPromise from "@payload-config";
import { RichText } from "@payloadcms/richtext-lexical/react";
import { getPayload } from "payload";
import type { FC } from "react";

export const CriticArchiveBlock: FC<ICriticArchiveBlockProps> = async () => {
	const payload = await getPayload({ config: configPromise });

	const critics = await payload.find({
		collection: "critics",
		limit: 0,
		depth: 2,
	});

	return (
		<section className="layout-block">
			<Accordion type="multiple">
				{critics.docs.map((critic, index) => (
					<AccordionItem key={critic.id} value={critic.id}>
						<AccordionTrigger>
							<h2>{critic.title}</h2>
							<div className="mt-8">
								<p>
									{typeof critic.concert === "object" && critic.concert
										? `Konzert ${critic.concert.title} am ${critic.concert.formattedDateString}`
										: null}
								</p>
								<p className="mt-4">
									<strong>{critic.subline}</strong>
								</p>
								<p>von {critic.author}</p>
							</div>
						</AccordionTrigger>
						<AccordionContent>
							<div className="default-grid">
								<div className="lg:col-start-4 lg:col-span-2  max-lg:my-4">
									{typeof critic.concert === "object" &&
									critic.concert &&
									typeof critic.concert.image === "object" ? (
										<Media
											resource={critic.concert.image}
											className="aspect-[3/4]"
											imgClassName="object-cover h-full w-full"
										/>
									) : null}
								</div>
								<div className="lg:col-start-6 lg:col-span-7">
									{critic.richText && <RichText data={critic.richText} />}
									<div className="pt-2 pb-4 lg:pt-8 lg:pb-16">
										<CMSLink {...critic.link} appearance="default" />
									</div>
								</div>
							</div>
						</AccordionContent>
						<div className="border-b border-secondary" />
					</AccordionItem>
				))}
			</Accordion>
		</section>
	);
};
