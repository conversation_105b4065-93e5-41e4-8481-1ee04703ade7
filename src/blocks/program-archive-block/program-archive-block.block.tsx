import type { FC } from "react";
import type {
	ProgramArchiveBlock as IProgramArchive<PERSON>lockProps,
	Program,
} from "@/payload-types";
import configPromise from "@payload-config";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/accordion";
import { getPayload } from "payload";
import RichText from "@/components/render/render-rich-text";

export const ProgramArchiveBlock: FC<IProgramArchiveBlockProps> = async ({
	limit: limitProp,
	populateBy,
	selectedDocs,
	sort: sortProp,
}) => {
	const limit = limitProp || 3;
	const sort = sortProp === "DESC" ? "-createdAt" : "createdAt";
	let programs: Program[] = [];

	if (populateBy === "all") {
		const payload = await getPayload({ config: configPromise });

		const fetchedPrograms = await payload.find({
			collection: "programs",
			depth: 1,
			limit,
			sort,
		});

		programs = fetchedPrograms.docs;
	} else {
		if (selectedDocs?.length) {
			const filteredSelectedPrograms = selectedDocs.map((post) => {
				if (typeof post.value === "object") return post.value;
			}) as Program[];

			programs = filteredSelectedPrograms;
		}
	}

	return (
		<section className="layout-block">
			<Accordion type="multiple">
				{programs.map((program) => (
					<AccordionItem key={program.id} value={program.id}>
						<AccordionTrigger className="border-b border-secondary">
							<h2 className="text-right">{program.title}</h2>
						</AccordionTrigger>
						<AccordionContent>
							<div className="default-grid border-b border-secondary">
								<div className="lg:col-span-4 lg:pt-4 max-lg:my-4">
									{program.description && (
										<RichText data={program.description} />
									)}
								</div>
								<div className="lg:col-start-6 lg:col-span-7 max-lg:pb-4">
									{program.segments?.map((segment, index) => (
										<div
											key={segment.id}
											className="py-4"
											style={{
												borderBottom:
													index === (program.segments?.length ?? 0) - 1
														? "none"
														: "1px solid var(--color-secondary)",
											}}
										>
											<p>{segment.title}</p>
										</div>
									))}
								</div>
							</div>
						</AccordionContent>
					</AccordionItem>
				))}
			</Accordion>
		</section>
	);
};
