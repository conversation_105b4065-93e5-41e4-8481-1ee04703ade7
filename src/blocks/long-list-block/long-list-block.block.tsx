import type { <PERSON> } from "react";
import type { LongListBlock as LongListBlockP<PERSON> } from "@/payload-types";

import { cn } from "@/lib/utils";
import RichText from "@/components/render/render-rich-text";
import { ParallaxImage } from "@/components/motion/parallax-image";

export const LongListBlock: FC<LongListBlockProps> = ({
	columns,
	image,
	title,
	richText,
	id,
}) => {
	return (
		<section className="relative lg:h-[100svh]">
			<div className="absolute inset-0 overflow-hidden">
				<ParallaxImage media={image} />
			</div>

			<div className="layout-block relative flex h-full flex-col justify-between py-6 lg:py-4">
				{/* --- MOBILE-ONLY: title + bottom text ABOVE the columns --- */}
				<div className="lg:hidden default-grid text-primary mb-6">
					<div className="col-span-12">
						<p className="text-3xl font-extrabold text-contrast">{title}</p>
					</div>
					<div className="col-span-12 mt-3">
						{richText && <RichText data={richText} enableGutter={false} />}
					</div>
				</div>

				{/* --- COLUMNS (always render; single col on mobile, multi on lg) --- */}
				<div className="default-grid text-primary text-xs">
					{columns?.length
						? columns.map((col, index) => {
								const { richText: rt, width = "2" } = col;
								const colSpan = `col-span-${width}`;
								return (
									<div
										key={`${id}-${index}`}
										className={cn("col-span-12", `lg:${colSpan}`)}
									>
										{rt && <RichText data={rt} enableGutter={false} />}
									</div>
								);
							})
						: null}
				</div>

				{/* --- DESKTOP-ONLY: absolute centered title --- */}
				<div className="hidden lg:block absolute left-0 right-0 top-1/2 -translate-y-1/2">
					<div className="default-grid">
						<div className="col-start-6 col-span-7">
							<p className="h2 text-contrast">{title}</p>
						</div>
					</div>
				</div>

				{/* --- DESKTOP-ONLY: bottom rich text aligned to right grid --- */}
				<div className="hidden lg:grid default-grid text-primary">
					<div className="col-start-6 col-span-7">
						{richText && <RichText data={richText} enableGutter={false} />}
					</div>
				</div>
			</div>
		</section>
	);
};
