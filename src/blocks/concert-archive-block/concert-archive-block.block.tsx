import { ConcertItem } from "@/components/concert-item";
import type {
	Concert,
	ConcertArchiveBlock as IConcertArchiveBlockProps,
} from "@/payload-types";
import configPromise from "@payload-config";
import { draftMode } from "next/headers";
import { getPayload } from "payload";
import type { FC } from "react";

const pattern = [
	"lg:col-start-5 lg:col-span-8",
	"lg:col-start-1 lg:col-span-4",
	"lg:col-start-5 lg:col-span-4",
	"lg:col-start-1 lg:col-span-8",
	"lg:col-start-9 lg:col-span-4",
];

export const ConcertArchiveBlock: FC<IConcertArchiveBlockProps> = async ({
	limit: limitProp = 1,
	populateBy,
	selectedDocs,
}) => {
	const payload = await getPayload({ config: configPromise });
	const { isEnabled: draft } = await draftMode();

	if (populateBy === "next") {
		const concert = await payload
			.find({
				collection: "concerts",
				depth: 1,
				draft,
				where: {
					_status: {
						equals: "published",
					},
					"dates.date": {
						greater_than_equal: new Date().toISOString(),
					},
				},
				sort: "dates.date",
				limit: 1,
			})
			.then((res) => res.docs[0]);

		if (!concert) {
			console.warn("No upcoming concert found");
			return null;
		}

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					<ConcertItem
						isMostRecent
						concert={concert}
						className="col-start-3 col-span-8"
					/>
				</div>
			</section>
		);
	}

	if (populateBy === "selection") {
		if (!selectedDocs?.length) {
			console.warn("No concerts selected");
			return null;
		}

		const concerts = selectedDocs.map((post) => {
			if (typeof post.value === "object") return post.value;
		}) as Concert[];

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					{concerts.map((concert, index) => (
						<ConcertItem
							key={concert.id}
							isMostRecent={index === 0}
							concert={concert}
							className={pattern[index % pattern.length]}
						/>
					))}
				</div>
			</section>
		);
	}

	if (populateBy === "past") {
		const concerts = await payload
			.find({
				collection: "concerts",
				depth: 1,
				draft,
				sort: "-dates.date",
				where: {
					_status: {
						equals: "published",
					},
					"dates.date": {
						less_than: new Date().toISOString(),
					},
				},
				limit: 0,
			})
			.then((res) => res.docs);

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					{concerts.map((concert, index) => (
						<ConcertItem
							key={concert.id}
							isMostRecent={index === 0}
							concert={concert}
							className={pattern[index % pattern.length]}
						/>
					))}
				</div>
			</section>
		);
	}

	const concerts = await payload
		.find({
			collection: "concerts",
			depth: 1,
			draft,
			sort: "dates.date",
			where:
				populateBy === "upcoming"
					? {
							_status: {
								equals: "published",
							},
							"dates.date": {
								greater_than_equal: new Date().toISOString(),
							},
						}
					: {
							_status: {
								equals: "published",
							},
						},
			limit: limitProp || 3,
		})
		.then((res) => res.docs);

	return (
		<section className="layout-block">
			<div className="default-grid gap-y-24">
				{concerts.map((concert, index) => (
					<ConcertItem
						key={concert.id}
						isMostRecent={index === 0}
						concert={concert}
						className={pattern[index % pattern.length]}
					/>
				))}
			</div>
		</section>
	);
};
