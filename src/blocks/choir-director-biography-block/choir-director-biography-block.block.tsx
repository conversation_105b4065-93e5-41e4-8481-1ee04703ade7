import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/accordion";
import { Media } from "@/components/render/render-media";
import { cn } from "@/lib/utils";
import type { ChoirDirectorBiographyBlock as ChoirDirectorBiographyBlockProps } from "@/payload-types";
import { RichText } from "@payloadcms/richtext-lexical/react";
import type { FC } from "react";

export const ChoirDirectorBiographyBlock: FC<
	ChoirDirectorBiographyBlockProps
> = ({ items }) => {
	return (
		<section className="layout-block">
			<Accordion type="multiple">
				<div className="border-b border-secondary" />
				{items?.length &&
					items.map(
						(item, i) =>
							item.id && (
								<AccordionItem key={item.id} value={item.id}>
									<AccordionTrigger>
										<div className="h2 text-right">{item.title}</div>
									</AccordionTrigger>
									<AccordionContent>
										<div className="default-grid lg:pt-8">
											<div className="lg:col-start-2 lg:col-span-4 max-lg:mb-4">
												{item.image && <Media resource={item.image} />}
											</div>
											<div className="lg:col-start-6 lg:col-span-7 pb-4 lg:pb-12">
												{item.richText && <RichText data={item.richText} />}
											</div>
										</div>
									</AccordionContent>
									<div className="border-b border-secondary" />
								</AccordionItem>
							),
					)}
			</Accordion>
		</section>
	);
};
