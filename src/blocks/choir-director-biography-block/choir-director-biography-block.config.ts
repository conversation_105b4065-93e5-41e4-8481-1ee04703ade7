import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";
import {
	FixedToolbarFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import type { Block } from "payload";

export const ChoirDirectorBiographyBlockConfig: Block = {
	slug: "choir-director-biography",
	imageURL: "/blocks/choir-director-biography-block.png",
	labels: {
		singular: "Chorleiter Biografie Sektion",
		plural: "Chorleiter Biografie Sektionen",
	},
	fields: [
		{
			name: "items",
			label: "Sektionen",
			type: "array",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
					required: true,
				},
				{
					name: "image",
					label: "Bild",
					type: "upload",
					relationTo: "media",
				},
				{
					name: "richText",
					label: "Biografie",
					type: "richText",
					editor: lexicalEditor({
						features: ({ rootFeatures }) => {
							return [...rootFeatures];
						},
					}),
				},
			],
		},
	],
	interfaceName: "ChoirDirectorBiographyBlock",
};
