@import "tailwindcss";
@import "tw-animate-css";

@source inline('{sm:,md:,lg:,xl:,}grid-cols-{1,2,3,4,5,6,7,8,9,10,11,12}');
@source inline('{sm:,md:,lg:,xl:,}col-start-{1,2,3,4,5,6,7,8,9,10,11,12}');
@source inline('{sm:,md:,lg:,xl:,}col-span-{1,2,3,4,5,6,7,8,9,10,11,12}');

@theme {
  --color-*: initial;
  --color-primary: #ffffff;
  --color-secondary: #000000;
  --color-contrast: #95C11F;
    
  /* --spacing-*: initial; */
  --spacing-0: 0;
	--spacing-safe: var(--safe);
	--spacing-gap: var(--gap);
  --spacing-header-height: var(--header-height);

  --font-*: initial;
  --font-sans: var(--font-sans);

  --animate-accordion-down: accordion-down 500ms var(--ease-in-out-expo);
  --animate-accordion-up: accordion-up 500ms var(--ease-in-out-expo);
  --animate-modal-in: modal-in 0.6s var(--ease-out-expo);
  --animate-modal-out: modal-out 0.6s var(--ease-out-expo);
  --animate-overlay-in: overlay-in 0.6s var(--ease-out-expo);
  --animate-overlay-out: overlay-out 0.6s var(--ease-out-expo);
  --animate-marquee: marquee 3s linear infinite;

  --ease-*: initial;
  --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
	--ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
	--ease-in-quart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
	--ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
	--ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
	--ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.335);
	--ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	--ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
	--ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
	--ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
	--ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
	--ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);
	--ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
	--ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
	--ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
	--ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
	--ease-in-out-expo: cubic-bezier(1, 0, 0, 1);
	--ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
	--ease-gleasing: cubic-bezier(0.4, 0, 0, 1);

  --width-columns-2: calc((2 * var(--column-width)) + (1 * var(--gap)));
  --width-columns-3: calc((3 * var(--column-width)) + (2 * var(--gap)));
  --width-columns-4: calc((4 * var(--column-width)) + (3 * var(--gap)));
  --width-columns-5: calc((5 * var(--column-width)) + (4 * var(--gap)));
  --width-columns-6: calc((6 * var(--column-width)) + (5 * var(--gap)));
}

@utility desktop-only {
  @media (width <= 799.98px) {
    display: none !important;
  }
}

@utility mobile-only {
  @media (width >= 800px) {
    display: none !important;
  }
}

@utility default-grid {
	display: grid;
	grid-template-columns: repeat(var(--columns), 1fr);
	gap: var(--gap);

  @media (width <= 799.98px) {
    grid-template-columns: 1fr;
  }
}

@utility layout-block {
	margin-inline: auto;
	width: calc(100% - 2 * 1.25rem);


}

@utility layout-block-inner {
	padding-inline: 1.25rem;
	width: 100%;
}

@utility layout-grid {
	@apply layout-block grid;
}

@utility layout-grid-inner {
	@apply layout-block-inner grid;
}