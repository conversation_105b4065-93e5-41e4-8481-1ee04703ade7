import type { FieldHook } from "payload";

export const formatSlug = (val: string): string =>
	val
		.normalize("NFKD") // Normalize characters like ü, ä etc.
		.replace(/ä/g, "ae")
		.replace(/ö/g, "oe")
		.replace(/ü/g, "ue")
		.replace(/Ä/g, "Ae")
		.replace(/Ö/g, "Oe")
		.replace(/Ü/g, "Ue")
		.replace(/ß/g, "ss")
		.replace(/ /g, "-")
		.replace(/[^\w-]+/g, "")
		.toLowerCase();

export const formatSlugHook =
	(fallbackField: string): FieldHook =>
	({ originalDoc, value, siblingData }) => {
		let baseValue = value;

		if (!baseValue && fallbackField in siblingData) {
			baseValue = siblingData[fallbackField];
		}

		if (!baseValue) {
			return originalDoc.slug;
		}

		const parts = baseValue.split("/");
		const parent = parts.slice(0, -1).join("/");
		const last = formatSlug(parts[parts.length - 1]);

		return parent ? `${parent}/${last}` : last;
	};
