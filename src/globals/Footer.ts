import { link } from "@/fields/link";
import type { GlobalConfig } from "payload";

export const Footer: GlobalConfig = {
	slug: "footer",
	access: {
		read: () => true,
	},
	fields: [
		{
			name: "navItems",
			label: "Navigationselemente",
			type: "array",
			maxRows: 6,
			fields: [
				{
					name: "hasChildren",
					label: "Gibts Unterseiten?",
					type: "checkbox",
					defaultValue: false,
				},
				{
					name: "label",
					type: "text",
					label: "Label",
					admin: {
						condition: (_, siblingData) => siblingData.hasChildren === true,
					},
				},
				{
					name: "children",
					label: "Unterseiten",
					type: "array",
					fields: [link({ appearances: false })],
					admin: {
						condition: (_, siblingData) => siblingData.hasChildren === true,
					},
				},
				link({
					appearances: false,
					overrides: {
						admin: {
							condition: (_, siblingData) => siblingData.hasChildren === false,
						},
					},
				}),
			],
		},
		{
			name: "bottomNavItems",
			label: "Untere Navigationselemente",
			type: "array",
			fields: [link({ appearances: false })],
		},
	],
};
